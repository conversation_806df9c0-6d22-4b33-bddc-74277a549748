package com.kbao.kbcelms.opportunity.vo;

import com.kbao.commons.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 机会数据导出VO
 * 用于Excel导出功能
 */
@Data
@ApiModel(description = "机会数据导出VO")
public class OpportunityExportVO {
    
    @Excel(name = "机会ID")
    private Integer id;
    
    @Excel(name = "机会名称")
    private String opportunityName;

    @Excel(name = "提交时间")
    private Date submitTime;

    @Excel(name = "业务渠道")
    private String opportunityType;

    @Excel(name = "机会状态")
    private String statusText;

    @Excel(name = "是否有历史保单")
    private String hasHistoryPolicyText;

    @Excel(name = "历史保单到期日期")
    private Date policyExpireTime;

    @Excel(name = "保费预算")
    private BigDecimal premiumBudget;

    @Excel(name = "预估投保人数")
    private Integer insureNum;

    @Excel(name = "顾问姓名")
    private String agentName;

    @Excel(name = "顾问工号")
    private String agentCode;

    @Excel(name = "所属法人公司编码")
    private String legalCode;

    @Excel(name = "所属法人公司")
    private String legalName;

    @Excel(name = "是否投标需要")
    private String isBidText;

    @Excel(name = "投标开始时间")
    private Date bidStartDate;

    @Excel(name = "投标结束时间")
    private Date bidEndDate;

    private String generalInsuranceType;
    @Excel(name = "客户需求")
    private String employeeInsuranceType;

    @Excel(name = "公司名称")
    private String companyName;

    @Excel(name = "社会统一信用代码")
    private String creditCode;

    @Excel(name = "企业所在城市")
    private String city;

    @Excel(name = "企业所属行业")
    private String categoryName;

    @Excel(name = "企业年收入")
    private String annualIncome;

    @Excel(name = "企业规模")
    private String enterpriseScale;

    @Excel(name = "企业类型")
    private String dtTypeText;

    @Excel(name = "企业对接人")
    private String contacter;

    @Excel(name = "企业对接人职务")
    private String contacterPost;

    @Excel(name = "KYC报告生成时间")
    private Date kycReportTime;

    @Excel(name = "统筹领取时间")
    private Date coordinationAcceptTime;

    @Excel(name = "指派统筹时间")
    private Date assignCoordinationTime;

    @Excel(name = "指派项目经理时间")
    private Date assignProjectManagerTime;

    @Excel(name = "项目经理")
    private String projectManagerName;

    @Excel(name = "组队完成时间")
    private Date teamTime;

    @Excel(name = "最新项目日志时间")
    private Date logTime;

    @Excel(name = "项目人员/分工/比例")
    private String projectTeamInfo;

    @Excel(name = "机会关闭时间")
    private Date closeTime;

    @Excel(name = "项目总结时间")
    private Date summaryTime;

    @Excel(name = "排分时间")
    private Date rankingTime;

    @Excel(name = "出单时间")
    private Date policyTime;

    @Excel(name = "暂停时间")
    private Date suspendTime;

    @Excel(name = "重启时间")
    private Date restartTime;
    //---------------------------------------

    /**
     * 状态映射
     */
    public static String getStatusText(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1:
                return "待提交";
            case 2:
                return "已完成";
            case 3:
                return "已终止";
            case 4:
                return "已暂停";
            case 5:
                return "已关闭";
            default:
                return "未知状态";
        }
    }
    
    /**
     * 投标状态映射
     */
    public static String getIsYesNoText(Object isBid) {
        if (isBid == null) {
            return "";
        }
        if (isBid instanceof  Integer){
            return Integer.valueOf(1).equals(isBid) ? "是" : "否";
        }
        else{
            return "1".equals(isBid) ? "是" : "否";
        }
    }
    
    /**
     * 历史保单状态映射
     */
    public static String getHasHistoryPolicyText(Integer hasHistoryPolicy) {
        if (hasHistoryPolicy == null) {
            return "";
        }
        return hasHistoryPolicy == 1 ? "有" : "无";
    }
    
    /**
     * 机会类型映射
     */
    public static String getOpportunityTypeText(String opportunityType) {
        if (opportunityType == null) {
            return "";
        }
        switch (opportunityType) {
            case "1":
                return "员服";
            case "2":
                return "综合";
            default:
                return opportunityType;
        }
    }

} 