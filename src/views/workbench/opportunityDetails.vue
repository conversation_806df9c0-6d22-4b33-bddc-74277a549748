<template>
  <div class="opportunity-details">
    <el-breadcrumb class="breadcrumb dt-bread" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item @click.native="handleBack">  {{ title }} </el-breadcrumb-item>
      <el-breadcrumb-item :style="{ color: themeObj.color }">
        机会详情
      </el-breadcrumb-item>
    </el-breadcrumb>
    <div class="nav-list">
      <div
        v-for="(item, index) in navBarlist"
        :key="index"
        :class="{ li: true, active: currentIndex == index }"
        :style="{
          color: currentIndex == index ? themeObj.color : '',
          'border-color': currentIndex == index ? themeObj.color : '',
        }"
        @click="navChange(item,index)"
      >
        {{ item.name }}
      </div>
    </div>
    <component :is="currentItem.componentName"></component>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { getOpportunityDetailFull } from "@/api/workbench";

import baseInfo from "./components/baseInfo";
import projectLog from "./components/projectLog";
import projectSummary from "./components/projectSummary";
import memberManage from "./components/memberManage";
import materialManage from "./components/materialManage";

export default {
  name: "opportunityDetails",
          provide() {
          return {
            userDetail: this,
            opportunityDetailFn: () => this.opportunityDetail,
            opportunityFn: () => this.opportunityDetail.opportunity,
            enterpriseFn: () => this.opportunityDetail.enterprise,
            opportunityProcessLogFn: () => this.opportunityDetail.opportunityProcessLog
          };
        },
  components: {
    TableToolTemp,
    baseInfo,
    projectLog,
    projectSummary,
    memberManage,
    materialManage
  },
  data() {
    return {
      navBarlist: [
       {
        name:"基本信息",
        componentName:"baseInfo"
       },
       {
        name:"成员管理",
        componentName:"memberManage"
       },
       {
        name:"资料管理",
        componentName:"materialManage"
       },
       {
        name:"项目日志",
        componentName:"projectLog"
       },
       {
        name:"项目总结",
        componentName:"projectSummary"
       }
      ],
      currentItem:{
        name:"基本信息",
        componentName:"baseInfo"
      },
      currentIndex:0,
      // 机会完整详情
      opportunityDetail: {
        enterprise: {}, // 客户信息
        opportunity: {}, // 机会基本信息
        opportunityProcessLog: {}, // 机会详细信息
      },
      title:"综合工作台",
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
  },
  watch: {},
  async created() {
    // 获取机会详情数据
    await this.getOpportunityDetailFullFun();
    let componentName = this.$route.query.componentName;
    if(componentName){
      this.currentItem.componentName = componentName;
      this.currentIndex = this.navBarlist.findIndex(item => item.componentName == componentName);
    }
    this.title = this.$route.query.title || "综合工作台";
    
  },
  mounted() {

  },
  methods: {
    handleBack(){
      this.$router.back();
    },
    navChange(item,index) {
      this.currentItem = item;
      this.currentIndex = index
    },
    
    // 获取机会详情完整信息
    async getOpportunityDetailFullFun() {
      try {
        let res = await getOpportunityDetailFull({
          opportunityId: this.$route.query.id
        });
        if (res) {
          this.opportunityDetail = res;
        }
      } catch (error) {
        console.error('获取机会详情失败:', error);
      }
    },
    
    // 更新机会详情数据的方法，供子组件调用
    updateOpportunityDetail(newData) {
      this.opportunityDetail = { ...this.opportunityDetail, ...newData };
    }
  },
};
</script>

<style lang="less">
.opportunity-details {
  .breadcrumb {
    padding: 13px 20px;
    border-bottom: 1px solid #eeeeee;
  }
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    margin-top: 20px;
    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;
      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }
  }
}
</style>
