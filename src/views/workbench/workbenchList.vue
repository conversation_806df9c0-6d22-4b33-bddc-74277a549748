<template>
  <div class="workbench-list">

    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>

    <div class="nav-list">
      <div
        v-for="(item, index) in navBarlist"
        :key="index"
        :class="{ li: true, active: activited == index }"
        :style="{
          color: activited == index ? themeObj.color : '',
          'border-color': activited == index ? themeObj.color : '',
        }"
        @click="navChange(index)" v-if="tabAuthCheck(item.name)"
      >
        {{ item.name }}
      </div>
    </div>

    <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
                @normalResetQuery="normalResetQuery"></SearchForm>

    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover>
      <el-table-column align="center" prop="opportunityName" label="机会名称"></el-table-column>
      <el-table-column align="center" prop="bizCode" label="机会ID"></el-table-column>
      <el-table-column align="center" prop="agentName" label="服务顾问"></el-table-column>
      <el-table-column align="center" prop="companyName" label="所属机构"></el-table-column>
      <el-table-column align="center" prop="salesCenterName" label="所属营业部"></el-table-column>
      <el-table-column align="center" prop="enterpriseName" label="企业名称"></el-table-column>
      <el-table-column align="center" prop="creditCode" label="社会统一信用代码"></el-table-column>
      <el-table-column align="center" prop="generalInsuranceType" label="客户需求"></el-table-column>
      <el-table-column align="center" prop="opportunityType" label="业务渠道">
        <template slot-scope="scope">
          <div>{{ opportunityTypes | getOpportunityTypeName(scope.row.opportunityType) }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="insureNum" label="预估投保人数"></el-table-column>
      <el-table-column align="center" prop="premiumBudget" label="保费预算"></el-table-column>
      <el-table-column align="center" prop="isBid" label="是否需要投标">
        <template slot-scope="scope">
          {{scope.row.isBid|getDicItemName("gen.yesorno.num")}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="submitTime" label="提交时间"></el-table-column>
      <el-table-column align="center" prop="status" label="机会状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 1">
            {{scope.row.processStep}}
          </span>
          <span v-else-if="scope.row.status == 4">
            {{ scope.row.status | getDicItemName("elms.opportunity.status") }} - {{ closeReasonTypes | getCloseReasonDesc(scope.row.closeReasonType) }}
          </span>
          <span v-else>
            {{ scope.row.status | getDicItemName("elms.opportunity.status") }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="lockStatus" label="锁定状态" v-if="activited == 4">
        <template slot-scope="scope">
          {{ scope.row.lockStatus | getDicItemName("elms.lock.status") }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="150px">
        <template slot-scope="scope">
          <el-button class="btn-center" type="text" @click="detail(scope.row)">机会详情</el-button>
          <el-button class="btn-center" type="text" @click="accept(scope.row)" v-if="activited == 0">领取机会</el-button>
          <el-button class="btn-center" type="text" @click="involved(scope.row)" v-if="activited == 1 && (!scope.row.participationStatus && scope.row.participationStatus != 0)">是否参与</el-button>
          <el-button class="btn-center" type="text" @click="confirm(scope.row)" v-if="activited == 1 && scope.row.participationStatus == 0">确认比例</el-button>
          <el-button class="btn-center" type="text" @click="restart(scope.row)" v-if="activited == 5 && (scope.row.status == 3 || (scope.row.status == 4 && scope.row.closeReasonType != 1 && hasRestartPermission))">重启机会</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
                :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

    <!-- 领取机会 -->
    <DtPopup :isShow.sync="showAcceptPopup" @close="closeAcceptPopup" title="领取机会" center :footer="false" width="600px">
      <div class="claim-confirm">
        <div class="confirm-content">
          <p>领取成功后，该机会归属您来跟进，其他人员不可见，确认是否领取该机会？</p>
        </div>
        <div class="confirm-actions">
          <el-button @click="closeAcceptPopup" style="width: 120px;">取消</el-button>
          <el-button type="primary" @click="confirmAccept" style="width: 120px;">确认领取</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 是否参与 -->
    <DtPopup :isShow.sync="showInvolvedPopup" @close="closeInvolvedPopup" title="是否参与该机会" center :footer="false" width="800px">
      <div class="involved-confirm">
        <div class="involved-content">
          <div>企业名称：{{ involvedData.enterpriseName }}</div>
        </div>
        <div class="involved-content">
          <div>机会ID：{{ involvedData.bizCode }}</div>
          <div>机会名称：{{ involvedData.opportunityName }}</div>
        </div>
        <div class="involved-content">
<!--          <div>{{ involvedData.projectOrgName }}</div>-->
<!--          <span style="color: red;padding:0 10px">{{ involvedData.projectManagerName }} </span>-->
<!--          <div style="padding-right: 10px">({{ involvedData.projectManagerId }})</div>-->
          <div>
            {{ involvedData.projectOrgName }}
            <span style="color: red;padding:0 5px">{{ involvedData.projectManagerName }} </span>
            ({{ involvedData.projectManagerId }})
            邀请您加入项目，可点击以下按钮，进行「参与」或「拒绝」操作。</div>
        </div>
        <div class="involved-actions">
          <el-button @click="rejectInvolved" style="width: 120px;">拒绝</el-button>
          <el-button type="primary" @click="confirmInvolved" style="width: 120px;">参与</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- “暂停机会”重启弹窗 -->
    <DtPopup :isShow.sync="showRestartPopup" @close="closeRestartPopup" title="重启机会" center :footer="false"
             width="700px">
      <div class="restart-form">
        <div class="restart-description">
          <p>确认重启后，该机会将恢复至正常推进状态，同时所有项目参与人员可见，是否确认本次操作？</p>
        </div>
        <div class="form-actions">
          <el-button @click="closeRestartPopup" style="width: 120px;">取消</el-button>
          <el-button type="primary" @click="confirmRestart" style="width: 120px;">确认重启</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- “已关闭 - 机会推进失败”重启弹窗 -->
    <DtPopup :isShow.sync="showRestartClosePopup" @close="closeRestartClosePopup" title="重启机会" center :footer="false"
             width="900px">
      <div class="popup-content">
        <!-- 提示文案 -->
        <div class="restart-close-tip">
          <p :style="{ color: themeObj.color }" >
            该机会已被 {{ closeReasonInfo.operatorOrgPath }} {{ closeReasonInfo.operatorUserName }}（{{
              closeReasonInfo.operatorName }}）变更为"已关闭-{{ closeReasonInfo.reasonTypeName }}"，是否确认重启并指派新的项目经理？
          </p>
        </div>

        <!-- 业务主体选择 -->
        <div class="business-type-section">
          <div class="section-title">业务主体</div>
          <el-radio-group v-model="restartCloseBusinessType" @change="handleRestartCloseBusinessTypeChange">
            <el-radio label="T0002">经纪</el-radio>
            <el-radio label="T0001">销售</el-radio>
          </el-radio-group>
        </div>

        <SearchForm @changeSelect="handleRestartCloseSearch" :searchForm="restartCloseSearchForm" :showSearch="false"
                    :searchFormTemp="restartCloseSearchFormTemp" />
        <div class="table-container">
          <el-table :data="restartCloseUserList" style="width: 100%;" @current-change="handleRestartCloseUserSelect">
            <el-table-column label="" width="50" align="center">
              <template slot-scope="scope">
                <input type="radio" :name="'restartCloseUserSelect'" :value="scope.row.userId"
                       v-model="selectedRestartCloseUserId" style="margin: 0;">
              </template>
            </el-table-column>
            <el-table-column prop="nickName" label="人员姓名" align="center" min-width="120" />
            <el-table-column prop="phone" label="手机号" align="center" min-width="130" />
            <el-table-column prop="email" label="邮箱" align="center" min-width="180" />
            <el-table-column prop="organName" label="人员归属" align="center" min-width="120" />
          </el-table>
        </div>

        <!-- 选中人员信息显示 -->
        <div class="selected-user-info" v-if="selectedRestartCloseUserInfo">
          <div class="info-item">
            <span class="label">人员姓名：</span>
            <span class="value">{{ selectedRestartCloseUserInfo.nickName }}</span>
          </div>
          <div class="info-item">
            <span class="label">参与机会数：</span>
            <span class="value">{{ selectedRestartCloseUserInfo.opportunityCount }}</span>
          </div>
          <div class="info-item">
            <span class="label">待完成任务数：</span>
            <span class="value">{{ selectedRestartCloseUserInfo.taskCount }}</span>
          </div>
        </div>

        <!-- 重启原因输入 -->
        <div class="restart-reason-section">
          <el-form ref="restartCloseFormRef" :model="restartCloseForm" :rules="restartCloseRules" label-width="0">
            <el-form-item prop="reasonDesc" required>
              <el-input type="textarea" v-model="restartCloseForm.reasonDesc" placeholder="请输入重启原因 (必填)" :rows="4"
                        maxlength="150" show-word-limit>
              </el-input>
            </el-form-item>
          </el-form>
        </div>

        <div class="form-actions">
          <el-button @click="closeRestartClosePopup" style="width: 120px;">取消</el-button>
          <el-button type="primary" @click="confirmRestartClose" :loading="restartCloseLoading" style="width: 120px;">确认重启</el-button>
        </div>
      </div>
    </DtPopup>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import * as api from "@/api/workbench/index.js";
import {getDicItemList, hasRights} from "@/config/tool.js";
import {findCompanyOrgData, findLegalOrgData} from "@/api/userManagement/index.js";
import {getBranchUsers, getLegalList} from "@/api/processManagement";
import {
  countUserParticipatedOpportunities,
  findLegalOrgDataByTenantId, getOpportunityStatus,
  restartOpportunity
} from "@/api/workbench/index.js";

export default {
  name: "workbenchList",
  data() {
    return {
      toolListProps: {
        toolTitle: "综合工作台",
        toolList: [
        ]
      },
      navBarlist: [
        {name: "待领取"},
        {name: "待参与"},
        {name: "待处理"},
        {name: "我参与"},
        {name: "锁定/解锁"},
        {name: "全部机会"},
      ],
      activited: 0,
      tableData: [{}],
      opportunityTypes:[{dicItemCode:"1",dicItemName:"员服"},{dicItemCode:"2",dicItemName:"综合"}],
      closeReasonTypes:[{dicItemCode:1,dicItemName:"机会已成交"},{dicItemCode:2,dicItemName:"机会推进失败"},{dicItemCode:3,dicItemName:"无效机会"}],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          activited: 0,
          tenantId: "",
          agentName: "",
          legalCode: "",
          opportunityName: "",
          bizCode: "",
          submitTimeStart: "",
          submitTimeEnd: "",
          companyName: "",
          companyId: "",
          opportunityType: "",
          processStep: "",
          lockStatus:""
        }
      },
      searchFormTemp: [
        {
          label: "顾问姓名/用户名",
          name: "agentName",
          type: "input",
          width: "200px"
        },
        {
          label: "所属机构",
          name: "legalCode",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "机会名称",
          name: "opportunityName",
          type: "input",
          width: "200px"
        },
        {
          label: "机会ID",
          name: "bizCode",
          type: "input",
          width: "200px"
        },
        {
          label: "企业名称",
          name: "enterpriseName",
          type: "input",
          width: "200px"
        },
        {
          label: "企业代码",
          name: "creditCode",
          type: "input",
          width: "200px"
        },
        {
          label: "业务渠道",
          name: "opportunityType",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "机会状态",
          name: "processStep",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          name: "createTime",
          type: "doubleDate",
          label: "机会提交时间",
          placeholder: "请选择",
          elType: "DateTimePicker",
          options: [
            {
              name: "submitTimeStart",
              placeholder: "请输入开始时间",
              value: "",
            },
            {
              name: "submitTimeEnd",
              placeholder: "请输入结束时间",
              value: "",
            },
          ],
          fixedShow: true,
        },
      ],
      total: 0,
      legalList: [],
      showAcceptPopup: false,
      opportunityId: "",
      showInvolvedPopup: false,
      involvedData: {
        opportunityId: "",
        opportunityName: "",
        bizCode: "",
        enterpriseName:"",
        projectOrgName: "",
        projectManagerId: "",
        projectManagerName: "",
      },

      // ================================重启弹窗属性 START =============================
      showRestartPopup:false,// 暂停机会重启弹窗
      restartData: {
        opportunityId: "",
        reason: ""
      },
      showRestartClosePopup:false, // 已关闭机会重启弹窗

      // 关闭机会原因对象
      closeReasonInfo: {
        operatorOrgPath: "",
        operatorUserName: "",
        operatorName: "",
        reasonTypeName: ""
      },
      restartCloseBusinessType: "T0001", // 默认选择销售
      restartCloseSearchForm: {
        pageNum: 1,
        pageSize: 10,
        param: {
          roleType: '3', // 默认选择分公司项目经理
          organCode: "",
          nickName: ''
        }
      },
      restartCloseSearchFormTemp: [
        { label: '指派对象', name: 'roleType', clearable: false, type: 'select', width: '140px', list: [{ dicItemCode: '3', dicItemName: '分公司项目经理' }, { dicItemCode: '4', dicItemName: '总公司项目经理' }, { dicItemCode: '7', dicItemName: '营业部内勤' }] },
        { label: '机构', name: 'organCode', clearable: false, type: 'select', width: '140px', list: [] },
        { label: '人员姓名', name: 'nickName', type: 'input', width: '140px' }
      ],
      restartCloseUserList: [],
      selectedRestartCloseUserId:"",
      selectedRestartCloseUserInfo: null, // 新增：用于存储选中人员的信息
      restartCloseForm: {
        reasonDesc: ""
      },
      restartCloseRules: {
        reasonDesc: [
          { required: true, message: "请输入重启原因", trigger: "blur" },
          { min: 1, max: 150, message: "长度在 1 到 150 个字符", trigger: "blur" }
        ]
      },
      restartCloseLoading: false,
      // 缓存接口数据
      cachedLegalOrgData: {},
      // ================================重启弹窗属性 END =============================
      // 推进失败重启权限
      hasRestartPermission: true,
      // 锁定状态数据
      lockStatusList: [],
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    tenantId() {
      return this.$store.state.layoutStore.currentLoginUser.tenantId;
    },
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
    // 获取企客用户权限信息
    qikeUserInfo() {
      return this.$store.getters["layoutStore/getQikeUserInfo"];
    },
    // navBarlist1(){
    //   let arr = [
    //     {name: "待领取"},
    //     {name: "待参与"},
    //     {name: "待处理"},
    //     {name: "我参与"},
    //     {name: "锁定/解锁"},
    //   ]
    //   if (!this.qikeUserInfo || !this.qikeUserInfo.roleAuths) {
    //     return false;
    //   }
    //   const roleAuths = this.qikeUserInfo.roleAuths;
    //
    //   let hasPermission = roleAuths.includes('elms:workbench:allOpportunity');
    //   if (hasPermission || this.currentLoginUser.isAdmin == '1') {
    //     arr.push({name: "全部机会"});
    //     // 推进失败重启权限
    //     this.hasRestartPermission = roleAuths.includes('elms:opportunity:restart:fail');
    //   }
    //   return arr;
    // },
  },
  filters: {
    getOpportunityTypeName(list, opportunityType) {
      let index = list.findIndex((v) => v.dicItemCode == opportunityType);
      return index > -1 ? list[index].dicItemName : "";
    },
    getCloseReasonDesc(list, closeReasonType) {
      let index = list.findIndex((v) => v.dicItemCode == closeReasonType);
      return index > -1 ? list[index].dicItemName : "";
    }
  },
  async created() {
    // await this.hasPermission();
    await this.getDicFun();
    await this.restartPermission();
    await this.initData();
  },
  activated() {
  },
  methods: {
    // 检查用户是否有指定权限
    tabAuthCheck(name){
      if(name == '全部机会') {
        if (!this.qikeUserInfo || !this.qikeUserInfo.roleAuths) {
          return false;
        }
        const roleAuths = this.qikeUserInfo.roleAuths;
        let hasPermission = roleAuths.includes('elms:workbench:allOpportunity');
        if (hasPermission || this.currentLoginUser.isAdmin == '1') {
          return true;
        }
        return false;
      }
      return true;
    },
    async restartPermission() {
      const roleAuths = this.qikeUserInfo.roleAuths;
      // 推进失败重启权限
      this.hasRestartPermission = roleAuths.includes('elms:opportunity:restart:fail');
    },
    async getDicFun() {

      await getDicItemList("elms.opportunity.status");
      await getDicItemList("gen.yesorno.num");
      this.lockStatusList = await getDicItemList("elms.lock.status");

      let legalListTemp = await findLegalOrgData({});

      legalListTemp.forEach(item => {
        this.legalList.push({
          dicItemName: item.orgName,
          dicItemCode: item.orgCode
        });
      });
      // 所属机构
      this.searchFormTemp[1].list = this.legalList;
      // 业务渠道
      this.searchFormTemp[6].list = this.opportunityTypes;
      // 状态
      this.searchFormTemp[7].list = await api.getOpportunityStatus({});
    },
    navChange(num) {
      this.activited = num;
      // 重置查询条件
      // this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initParam.param.activited = num;
      if (this.activited == 4) {
        this.searchFormTemp.splice(this.searchFormTemp.length-1,0,
          {
            label: "锁定状态",
            name: "lockStatus",
            type: "select",
            width: "200px",
            list: this.lockStatusList
          }
        );
        this.searchFormTemp[this.searchFormTemp.length-1].list = this.lockStatusList;
      } else {
        this.initParam.param.lockStatus = "";// 其他tab没有锁定状态查询条件
        const index = this.searchFormTemp.findIndex(item => item.name === 'lockStatus');
        if (index > -1) {
          this.searchFormTemp.splice(index, 1)
        }
      }
      this.initData();
    },
    async initData() {
      this.initParam.param.tenantId = this.$store.state.layoutStore.currentLoginUser.tenantId;
      // this.tableData = [
      //   { "opportunityId":2,"opportunityName": "海底捞-员福-2025-关闭", "agentCode": "6400000001", "agentName": "徐晨旭", "companyName": "北京分公司","salesCenterName":"北京营业部","enterpriseName":"某企业有限公司","creditCode":"91110000123456789X","generalInsuranceType":"员工意外险","isBid":"1","insureNum":1000,"premiumBudget":50000,"processStep":"机会提交","resaonDesc":"已成交","submitTime":"2025-07-31 11:22:00","status":"4" },
      //   { "opportunityId":2,"opportunityName": "海底捞-员福-2025-待提交", "agentCode": "6400000001", "agentName": "徐晨旭", "companyName": "北京分公司","salesCenterName":"北京营业部","enterpriseName":"某企业有限公司","creditCode":"91110000123456789X","generalInsuranceType":"员工意外险","isBid":"1","insureNum":1000,"premiumBudget":50000,"processStep":"机会提交","resaonDesc":"","submitTime":"2025-07-31 11:22:00","status":"0" },
      //   { "opportunityId":2,"opportunityName": "海底捞-员福-2025-已提交", "agentCode": "6400000001", "agentName": "徐晨旭", "companyName": "北京分公司","salesCenterName":"北京营业部","enterpriseName":"某企业有限公司","creditCode":"91110000123456789X","generalInsuranceType":"员工意外险","isBid":"1","insureNum":1000,"premiumBudget":50000,"processStep":"机会提交","resaonDesc":"已成交","submitTime":"2025-07-31 11:22:00","status":"1" },
      //   { "opportunityId":2,"opportunityName": "海底捞-员福-2025-锁定", "agentCode": "6400000001", "agentName": "徐晨旭", "companyName": "北京分公司","salesCenterName":"北京营业部","enterpriseName":"某企业有限公司","creditCode":"91110000123456789X","generalInsuranceType":"员工意外险","isBid":"1","insureNum":1000,"premiumBudget":50000,"processStep":"机会提交","resaonDesc":"已成交","submitTime":"2025-07-31 11:22:00","status":"2" },
      //   { "opportunityId":2,"opportunityName": "海底捞-员福-2025-暂停", "agentCode": "6400000001", "agentName": "徐晨旭", "companyName": "北京分公司","salesCenterName":"北京营业部","enterpriseName":"某企业有限公司","creditCode":"91110000123456789X","generalInsuranceType":"员工意外险","isBid":"1","insureNum":1000,"premiumBudget":50000,"processStep":"机会提交","resaonDesc":"已成交","submitTime":"2025-07-31 11:22:00","status":"3" },
      // ]
      // this.total = 1;

      let res;
      if (this.activited == 0) {// 待领取
        res = await api.toGetPage(this.initParam);
      } else if (this.activited == 1) {// 待参与
        res = await api.toJoinPage(this.initParam);
      } else if (this.activited == 2) {// 待处理
        res = await api.toDoPage(this.initParam);
      } else if (this.activited == 3) {// 我参与
        res = await api.toHasJoinPage(this.initParam);
      } else if (this.activited == 4) {// 锁定解锁
        res = await api.lockedPage(this.initParam);
      } else if (this.activited == 5) {// 全部机会
        res = await api.toALLPage(this.initParam);
      }
      if (res) {
        this.total = res.total;
        this.tableData = [];
        if (res.list) {
          this.tableData = res.list ? res.list : [{}];
        }
      }
    },
    handleTool(item) {
    },
    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.searchFormTemp = this.$options.data().searchFormTemp;
      this.initData();
      this.getDicFun();
    },
    detail(row) {
      this.$router.push({
        name: "opportunityDetails",
        query: {
          id: row.opportunityId
        }
      });
    },
    accept(row){
      this.opportunityId = row.opportunityId;
      this.showAcceptPopup = true;
    },
    closeAcceptPopup() {
      this.showAcceptPopup = false;
    },
    async confirmAccept() {
      let res = await api.acceptTask({opportunityId: this.opportunityId});
      if (res) {
        this.$message.success("机会领取成功");
      }
      this.initData();
      this.showAcceptPopup = false;
    },
    async involved(row) {
      this.opportunityId = row.opportunityId;
      this.involvedData.opportunityId = row.opportunityId;
      this.involvedData.bizCode = row.bizCode;
      this.involvedData.opportunityName = row.opportunityName;
      this.involvedData.enterpriseName = row.enterpriseName;

      let res = await api.getOpportunityInviterInfo({opportunityId: row.opportunityId});
      if(res) {
        this.involvedData.projectManagerId = res.projectManagerId;
        this.involvedData.projectManagerName = res.projectManagerName;
        this.involvedData.projectOrgName = res.projectOrgName;
      }
      this.showInvolvedPopup = true;
    },
    async confirmInvolved() {
      let res = await api.acceptOrReject({opportunityId: this.opportunityId, joinType: 1});
      if(res) {
        this.$message.success("参与成功");
      }
      this.showInvolvedPopup = false;
      this.initData();
    },
    async rejectInvolved() {
      let res = await api.acceptOrReject({opportunityId: this.opportunityId, joinType: 5});
      if(res) {
        this.$message.success("拒绝成功");
      }
      this.showInvolvedPopup = false;
      this.initData();
    },
    closeInvolvedPopup() {
      this.showInvolvedPopup = false;
    },
    async confirm(row) {
      this.$router.push({
        name: "opportunityDetails",
        query: {
          id: row.opportunityId,
          componentName:"memberManage"
        }
      });
    },
    // ===========================重启机会相关方法 START =================================
    async restart(row) {// 列表确认重启按钮方法
      this.restartData.opportunityId = row.opportunityId;
      // 判断是暂停机会重启还是关闭机会重启 , 显示不同的弹窗窗口
      if (3 == row.status) {
        this.showRestartPopup = true;
        this.restartData.reason = "暂停机会重启";
      } else {
        this.showRestartClosePopup = true;
        // 重新查询机构
        await this.handleGetLegalListFun();

        // 设置默认的指派对象为第一个选项（通常是分公司项目经理）
        if (this.restartCloseSearchFormTemp[0].list && this.restartCloseSearchFormTemp[0].list.length > 0) {
          this.restartCloseSearchForm.param.roleType = this.restartCloseSearchFormTemp[0].list[0].dicItemCode;
        }
        // 根据默认的指派对象设置机构列表
        const tenantId = this.restartCloseBusinessType;
        if (this.cachedLegalOrgData[tenantId]) {
          this.updateOrgListByRoleType(this.restartCloseSearchForm.param.roleType, this.restartCloseSearchFormTemp, this.cachedLegalOrgData[tenantId]);
          // 设置默认的机构为第一个选项
          if (this.restartCloseSearchFormTemp[1].list && this.restartCloseSearchFormTemp[1].list.length > 0) {
            this.restartCloseSearchForm.param.organCode = this.restartCloseSearchFormTemp[1].list[0].dicItemCode;
          }
        }
        this.restartCloseSearchForm.param.nickName = ""

        this.handleRestartCloseSearchFun();
        // 获取关闭原因信息
        await this.getCloseReasonInfo();
      }
    },

    // 获取关闭原因信息
    async getCloseReasonInfo() {
      try {
        const res = await api.getCloseReason({
          opportunityId: this.restartData.opportunityId
        });
        if (res) {
          this.closeReasonInfo = res;
        } else {
          this.closeReasonInfo = this.$options.data().closeReasonInfo;
        }
      } catch (error) {
        this.closeReasonInfo = this.$options.data().closeReasonInfo;
      }
    },

    // 暂停机会重启 - 关闭弹窗方法
    closeRestartPopup() {
      this.showRestartPopup = false;
    },
    // 暂停机会重启 - 确认重启方法
    async confirmRestart() {
      let res = await api.resume(this.restartData);
      this.showRestartPopup = false;
      this.$message.success("机会重启成功");
      this.initData();
    },

    closeRestartClosePopup() {//已关闭机会重启 -  关闭弹窗方法
      this.showRestartClosePopup = false;
      this.selectedRestartCloseUserId = "";
      this.selectedRestartCloseUserInfo = this.$options.data().selectedRestartCloseUserInfo; // 关闭弹窗时清空选中信息
      this.closeReasonInfo = this.$options.data().closeReasonInfo; // 清空关闭原因信息
      this.restartCloseBusinessType = "T0001"; // 重置为默认选择
      this.restartCloseForm.reasonDesc = "";
      this.$nextTick(() => {
        if (this.$refs.restartCloseFormRef) {
          this.$refs.restartCloseFormRef.resetFields();
        }
      });
    },

    async confirmRestartClose() {
      if (!this.selectedRestartCloseUserId) {
        this.$message.warning("请选择指派人员");
        return;
      }

      try {
        await this.$refs.restartCloseFormRef.validate();
        this.restartCloseLoading = true;
        let res = await api.restartOpportunity({
          opportunityId: this.restartData.opportunityId ,
          assignee: this.selectedRestartCloseUserId,
          assigneeRoleType: this.restartCloseSearchForm.param.roleType,
          assigneeOrg: this.restartCloseSearchForm.param.organCode,
          businessTenantId: this.restartCloseBusinessType,
          reasonDesc: this.restartCloseForm.reasonDesc
        });
        if (res) {
          // this.opportunityStatus = "normal"; // 改变状态为正常
          this.$message.success("机会重启成功");
          this.closeRestartClosePopup();
          this.initData();
        }
      } catch (error) {
      } finally {
        this.restartCloseLoading = false;
      }
    },

    // 业务主体变化处理
    async handleRestartCloseBusinessTypeChange(value) {
      // 重新查询机构
      await this.handleGetLegalListFun(value);
      // 清空人员姓名
      this.restartCloseSearchForm.param.nickName = "";
      // 重置指派对象选择为第一个选项
      if (this.restartCloseSearchFormTemp[0].list && this.restartCloseSearchFormTemp[0].list.length > 0) {
        this.restartCloseSearchForm.param.roleType = this.restartCloseSearchFormTemp[0].list[0].dicItemCode;
      }
      // 根据新的指派对象设置机构列表
      if (this.cachedLegalOrgData[value]) {
        this.updateOrgListByRoleType(this.restartCloseSearchForm.param.roleType, this.restartCloseSearchFormTemp, this.cachedLegalOrgData[value]);
        // 重置机构选择为第一个选项
        if (this.restartCloseSearchFormTemp[1].list && this.restartCloseSearchFormTemp[1].list.length > 0) {
          this.restartCloseSearchForm.param.organCode = this.restartCloseSearchFormTemp[1].list[0].dicItemCode;
        }
      }
      // 清空之前选中的用户状态
      this.selectedRestartCloseUserId = "";
      this.selectedRestartCloseUserInfo = null;
      // 重新调用查询用户
      this.handleRestartCloseSearchFun();
    },

    async handleGetLegalListFun(tenantIdParam) {
      const tenantId = tenantIdParam || "T0001";

      // 检查缓存中是否已有数据
      if (!this.cachedLegalOrgData[tenantId]) {
        const res = await api.findLegalOrgDataByTenantId({ tenantId });
        if (res) {
          this.cachedLegalOrgData[tenantId] = res;
        } else {
          return;
        }
      }

      const res = this.cachedLegalOrgData[tenantId];

      // 根据orgType判断是否为总公司
      const isHeadquarters = res.orgType === 'dept';

      // 根据不同的业务场景更新对应的指派对象选项
      // if (!tenantIdParam) {
      //   // 指派统筹场景
      //   // 根据orgType动态调整指派对象选项
      //   if (isHeadquarters) {
      //     // 总公司场景：包含总公司统筹
      //     this.restartCloseSearchFormTemp[0].list = [
      //       { dicItemCode: '1', dicItemName: '分公司统筹' },
      //       { dicItemCode: '2', dicItemName: '总公司统筹' }
      //     ];
      //   } else {
      //     // 分公司场景：只显示分公司统筹
      //     this.restartCloseSearchFormTemp[0].list = [{ dicItemCode: '1', dicItemName: '分公司统筹' }];
      //   }
      //   // 机构列表根据当前选择的指派对象来决定
      //   this.updateOrgListByRoleType(this.assignSearchForm.param.roleType, this.restartCloseSearchFormTemp, res);
      // } else if (tenantIdParam === this.restartCloseBusinessType) {
        // 重启关闭机会场景
        // 根据orgType动态调整指派对象选项
        if (isHeadquarters) {
          // 总公司场景：包含总公司项目经理
          this.restartCloseSearchFormTemp[0].list = [
            { dicItemCode: '3', dicItemName: '分公司项目经理' },
            { dicItemCode: '4', dicItemName: '总公司项目经理' },
            { dicItemCode: '7', dicItemName: '营业部内勤' }
          ];
        } else {
          // 分公司场景：显示分公司项目经理和营业部内勤
          this.restartCloseSearchFormTemp[0].list = [
            { dicItemCode: '3', dicItemName: '分公司项目经理' },
            { dicItemCode: '7', dicItemName: '营业部内勤' }
          ];
        }
        // 机构列表根据当前选择的指派对象来决定
        this.updateOrgListByRoleType(this.restartCloseSearchForm.param.roleType, this.restartCloseSearchFormTemp, res);
      // }
    },

    // 根据指派对象类型更新机构列表
    updateOrgListByRoleType(roleType, searchFormTemp, res) {
      // 判断是否为总公司相关选项
      const isHeadquartersRole = roleType === '2' || roleType === '4'; // 2:总公司统筹, 4:总公司项目经理

      if (isHeadquartersRole) {
        // 选择总公司相关选项时，机构列表固定为总公司
        searchFormTemp[1].list = [{ dicItemCode: "", dicItemName: "总公司" }];
      } else {
        // 选择其他选项时，使用接口返回的orgList
        const orgList = res.orgList ? res.orgList.map(item => ({ dicItemCode: item.orgCode, dicItemName: item.orgName })) : [];
        searchFormTemp[1].list = orgList;
      }
    },

    async handleRestartCloseSearchFun() {
      this.managerSearchForm.param.tenantId = this.restartCloseBusinessType;
      let res = await getBranchUsers(this.restartCloseSearchForm.param);
      if (res && _.isArray(res)) {
        this.restartCloseUserList = res;
      } else {
        this.restartCloseUserList = [];
      }
      // 清空之前选中的用户状态
      this.selectedRestartCloseUserId = "";
      this.selectedRestartCloseUserInfo = null;
    },

    // 重启关闭机会搜索
    handleRestartCloseSearch(item, data) {
      this.restartCloseSearchForm = data;

      // 如果是指派对象变化，需要更新机构列表
      if (item && item.name === 'roleType') {
        this.handleRoleTypeChange(data.param.roleType, this.restartCloseSearchFormTemp, this.restartCloseSearchForm);
      }

      this.handleRestartCloseSearchFun();
    },

    // 处理指派对象变化时的机构列表更新
    async handleRoleTypeChange(roleType, searchFormTemp, searchForm) {
      // 判断是否为总公司相关选项
      const isHeadquartersRole = roleType === '2' || roleType === '4'; // 2:总公司统筹, 4:总公司项目经理

      if (isHeadquartersRole) {
        // 选择总公司相关选项时，机构列表固定为总公司
        searchFormTemp[1].list = [{ dicItemCode: "", dicItemName: "总公司" }];
        searchForm.param.organCode = "";
      } else {
        // 选择其他选项时，使用缓存数据更新机构列表
        // 根据不同的搜索表单确定对应的业务类型
        let tenantIdParam = null;
        if (searchFormTemp === this.assignSearchFormTemp) {
          tenantIdParam = null; // 指派统筹场景
        } else if (searchFormTemp === this.managerSearchFormTemp) {
          tenantIdParam = this.restartCloseBusinessType;
        } else if (searchFormTemp === this.restartCloseSearchFormTemp) {
          tenantIdParam = this.restartCloseBusinessType;
        }

        const tenantId = tenantIdParam || "T0001";

        // 检查缓存中是否已有数据
        if (!this.cachedLegalOrgData[tenantId]) {
          const res = await api.findLegalOrgDataByTenantId({ tenantId });
          if (res) {
            this.cachedLegalOrgData[tenantId] = res;
          } else {
            return;
          }
        }

        const res = this.cachedLegalOrgData[tenantId];
        this.updateOrgListByRoleType(roleType, searchFormTemp, res);
        // 更新机构选择为第一个可用选项
        if (searchFormTemp[1].list && searchFormTemp[1].list.length > 0) {
          searchForm.param.organCode = searchFormTemp[1].list[0].dicItemCode;
        }
      }

      // 清空之前选中的用户状态
      if (searchFormTemp === this.assignSearchFormTemp) {
        this.selectedAssignUserId = "";
        this.selectedUserInfo = null;
      } else if (searchFormTemp === this.managerSearchFormTemp) {
        this.selectedManagerUserId = "";
        this.selectedManagerUserInfo = null;
      } else if (searchFormTemp === this.restartCloseSearchFormTemp) {
        this.selectedRestartCloseUserId = "";
        this.selectedRestartCloseUserInfo = null;
      }
    },

    async handleRestartCloseUserSelect(row) {
      if (!row) {
        this.selectedRestartCloseUserInfo = null;
        return;
      }
      this.selectedRestartCloseUserId = row.userId;
      try {
        // 调用接口获取参与机会数和待完成任务数
        const res = await api.countUserParticipatedOpportunities({
          userId: row.userId
        });

        if (res) {
          this.selectedRestartCloseUserInfo = {
            ...row,
            opportunityCount: res.opportunityCount || 0,
            taskCount: res.taskCount || 0
          };
        } else {
          // 如果接口调用失败，使用默认值
          this.selectedRestartCloseUserInfo = {
            ...row,
            opportunityCount: 0,
            taskCount: 0
          };
        }
      } catch (error) {
        // 接口调用失败时使用默认值
        this.selectedRestartCloseUserInfo = {
          ...row,
          opportunityCount: 0,
          taskCount: 0
        };
      }
    },

    // ===========================重启机会相关方法 END =================================

    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    }
  }
};
</script>
<style lang="less">
.workbench-list {
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    width: 100%;
    margin-bottom: 20px;

    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;

      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }
  }

  // 领取机会弹窗样式
  .claim-confirm {
    padding: 0 20px 20px 20px;

    .confirm-content {
      margin-bottom: 20px;
      text-align: center;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }

  // 参与机会弹窗样式
  .involved-confirm {
    //padding: 0 20px 20px 20px;
    //line-height: 55px;
    //display: flex;

    .involved-content {
      display: flex;
      line-height: 55px;
      //margin-bottom: 20px;
      //text-align: center;
      //
      //p {
      //  margin: 0;
      //  color: #606266;
      //  font-size: 14px;
      //  line-height: 1.6;
      //}
      div {
        margin-right: 50px;
      }
    }

    .involved-content2 {
      display: flex;
    }

    .involved-actions {
      //display: flex;
      //justify-content: center;
      text-align: center;
      margin-bottom: 20px;
      margin-top: 10px;
      //gap: 12px;
    }
  }

  // 重启暂停机会弹窗样式
  .restart-form {
    padding: 0 20px 20px 20px;

    .restart-description {
      margin-bottom: 20px;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
    }
  }

  // 重启关闭机会弹窗样式
  .assign-content {
    padding: 0 20px 20px 20px;

    .business-type-section {
      margin-bottom: 20px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 4px;

      .section-title {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 12px;
        color: #333;
      }

      .el-radio-group {
        .el-radio {
          margin-right: 20px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .table-container {
      max-height: 400px;
      overflow-y: auto;
      margin-top: 10px;

      // 隐藏滚动条
      &::-webkit-scrollbar {
        width: 0;
        display: none;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: transparent;
      }

      // Firefox
      scrollbar-width: none;

      // IE
      -ms-overflow-style: none;
    }

    .restart-close-tip {
      margin-bottom: 20px;
      padding: 16px;
      background: #f9f9f9;
    ;
      border-radius: 4px;

      p {
        margin: 0;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
    }
  }


  // 通用弹窗内容样式
  .popup-content {
    padding: 0 20px 20px 20px;
    max-height: 600px;
    overflow-y: auto;

    // 在小屏幕上调整最大高度
    @media (max-height: 800px) {
      max-height: 500px;
    }

    @media (max-height: 600px) {
      max-height: 400px;
    }

    .business-type-section {
      margin-bottom: 20px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 4px;

      .section-title {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 12px;
        color: #333;
      }

      .el-radio-group {
        .el-radio {
          margin-right: 20px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .table-container {
      margin-top: 10px;
      margin-bottom: 10px;
      max-height: 300px;
      overflow-y: auto;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }

      // Firefox
      scrollbar-width: thin;
      scrollbar-color: #c1c1c1 #f1f1f1;

      // IE
      -ms-overflow-style: auto;

      // 为表格设置固定高度和滚动
      .el-table {
        .el-table__body-wrapper {
          overflow-y: auto;
        }

        // 确保表格在小屏幕上也能正常显示
        .el-table__header-wrapper {
          background-color: #f5f7fa;
        }

        .el-table__body {
          .el-table__row {
            &:hover {
              background-color: #f5f7fa;
            }
          }
        }
      }
    }

    .restart-close-tip {
      margin-bottom: 20px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 4px;

      p {
        margin: 0;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
    }
  }

  // 选中人员信息显示样式
  .selected-user-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f2f5;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .info-item {
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 200px;

      .label {
        font-weight: bold;
        margin-right: 8px;
        min-width: 80px;
      }

      .value {
        color: v-bind('themeObj.color');
        flex: 1;
      }
    }

    // 在小屏幕上调整布局
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 10px;

      .info-item {
        flex-direction: column;
        align-items: flex-start;
        min-width: auto;

        .label {
          margin-bottom: 4px;
          min-width: auto;
        }
      }
    }
  }


  // 重启原因输入样式
  .restart-reason-section {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .el-form-item {
      margin-bottom: 0;
    }

    .el-textarea {
      .el-textarea__inner {
        border: 1px solid #dcdfe6;
        border-radius: 4px;

        &:focus {
          border-color: #409eff;
        }
      }
    }
  }
}

</style>
